{"name": "migranium-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@tailwindcss/vite": "^4.1.10", "@tanstack/react-query": "^4.39.2", "@tanstack/react-query-devtools": "^4.39.2", "axios": "^1.10.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "init": "^0.1.2", "lucide-react": "^0.516.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.58.1", "react-router": "^7.6.2", "shadcn": "^2.6.4", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.10", "zod": "^3.25.67", "zustand": "^5.0.5"}, "devDependencies": {"@chromatic-com/storybook": "4.0.0", "@eslint/js": "^9.25.0", "@storybook/addon-a11y": "9.0.11", "@storybook/addon-docs": "9.0.11", "@storybook/addon-onboarding": "9.0.11", "@storybook/addon-vitest": "9.0.11", "@storybook/react-vite": "9.0.11", "@types/node": "^24.0.3", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "@vitest/browser": "^3.2.3", "@vitest/coverage-v8": "^3.2.3", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "eslint-plugin-storybook": "9.0.11", "globals": "^16.0.0", "playwright": "^1.53.0", "storybook": "9.0.11", "tw-animate-css": "^1.3.4", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5", "vitest": "^3.2.3"}, "packageManager": "pnpm@9.15.3+sha512.1f79bc245a66eb0b07c5d4d83131240774642caaa86ef7d0434ab47c0d16f66b04e21e0c086eb61e62c77efc4d7f7ec071afad3796af64892fae66509173893a"}