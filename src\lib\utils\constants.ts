// API Configuration
export const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001/api'

// Application Routes
export const ROUTES = {
  HOME: '/',
  DASHBOARD: '/dashboard',
  CUSTOMERS: '/customers',
  WAITLIST: '/waitlist',
  SCHEDULE: '/schedule',
  ANALYTICS: '/analytics',
  SETTINGS: '/settings',
  AUTH: {
    SIGNIN: '/auth/signin',
    SIGNUP: '/auth/signup',
  },
} as const

// Theme Configuration
export const THEME = {
  DEFAULT: 'light',
  DARK: 'dark',
  LIGHT: 'light',
} as const

// Storage Keys
export const STORAGE_KEYS = {
  AUTH_TOKEN: 'migranium_auth_token',
  USER_PREFERENCES: 'migranium_user_preferences',
  THEME: 'migranium_theme',
} as const

// Validation Constants
export const VALIDATION = {
  EMAIL_REGEX: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  PHONE_REGEX: /^\+?[\d\s\-()]+$/,
  MIN_PASSWORD_LENGTH: 8,
} as const

// Pagination
export const PAGINATION = {
  DEFAULT_PAGE_SIZE: 10,
  PAGE_SIZE_OPTIONS: [10, 20, 50, 100],
} as const 