import axios from 'axios'
import { useAuthStore } from '@/stores/authStore'
import { useUIStore } from '@/stores/uiStore'

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL

export const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// Request interceptor - Add auth token
apiClient.interceptors.request.use(
  (config) => {
    const token = useAuthStore.getState().token
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => Promise.reject(error)
)

// Response interceptor - Handle errors globally
apiClient.interceptors.response.use(
  (response) => response,
  async (error) => {
    const { addToast } = useUIStore.getState()
    const { logout } = useAuthStore.getState()
    
    if (error.response?.status === 401) {
      logout()
      addToast({
        type: 'error',
        title: 'Session Expired',
        message: 'Please log in again'
      })
      window.location.href = '/auth/signin'
    } else if (error.response?.status >= 500) {
      addToast({
        type: 'error',
        title: 'Server Error',
        message: 'Something went wrong. Please try again.'
      })
    }
    
    return Promise.reject(error)
  }
)